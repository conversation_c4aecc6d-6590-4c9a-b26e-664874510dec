"""
科大讯飞实时语音转写管理器
专门用于实时流式转录分析，支持AI主动打断功能
"""

import asyncio
import json
import hashlib
import hmac
import base64
import time
import websockets
from datetime import datetime
from loguru import logger
from typing import Optional, Callable, Dict, Any
from urllib.parse import quote

from .config import settings
from .message_bus import message_bus, TopicNames, create_message
from .event_types import EventType, MessageType


class XunfeiASRManager:
    def __init__(self):
        self.app_id = settings.xunfei_app_id
        self.api_key = settings.xunfei_api_key
        self.base_url = settings.xunfei_asr_url
        self.chunk_size = settings.xunfei_chunk_size
        self.chunk_interval = settings.xunfei_chunk_interval_ms / 1000.0
        
        self.websocket = None
        self.is_connected = False
        self.is_session_active = False
        self.current_session_id = None
        
        # 🎯 性能优化：音频缓冲区配置
        self.audio_buffer = asyncio.Queue(maxsize=100)  # 限制队列大小防止内存溢出
        self.send_task = None
        self.recv_task = None
        
        # 🎯 性能优化：批量发送配置
        self.batch_send_enabled = True
        self.batch_buffer = bytearray()
        self.batch_threshold = self.chunk_size * 2  # 批量发送阈值
        
        # 🎯 增强错误处理：连接管理
        self.connection_attempts = 0
        self.max_connection_attempts = 5
        self.reconnect_delay = 2.0
        self.connection_stable = False
        self.last_error_time = None
        self.error_count = 0
        self.max_errors_before_disable = 10  # 达到此错误数后暂时禁用
        
    def _generate_signature(self, ts: str) -> str:
        """生成科大讯飞API签名"""
        tt = (self.app_id + ts).encode('utf-8')
        md5 = hashlib.md5()
        md5.update(tt)
        base_string = md5.hexdigest()
        base_string = bytes(base_string, encoding='utf-8')
        
        api_key = self.api_key.encode('utf-8')
        signa = hmac.new(api_key, base_string, hashlib.sha1).digest()
        signa = base64.b64encode(signa)
        return str(signa, 'utf-8')
    
    async def connect(self, session_id: str) -> bool:
        """建立与科大讯飞的WebSocket连接 - 增强版错误处理"""
        # 🎯 错误处理：检查错误计数是否超限
        if self.error_count >= self.max_errors_before_disable:
            logger.warning(f"⚠️ 科大讯飞错误次数过多({self.error_count})，暂时禁用")
            return False
            
        try:
            self.current_session_id = session_id
            self.connection_attempts += 1
            
            # 🎯 错误处理：验证基本配置
            if not self.app_id or not self.api_key:
                logger.error("❌ 科大讯飞凭证未配置，无法建立连接")
                return False
            
            ts = str(int(time.time()))
            signa = self._generate_signature(ts)
            
            url = f"{self.base_url}?appid={self.app_id}&ts={ts}&signa={quote(signa)}"
            
            logger.info(f"🔗 连接科大讯飞ASR (尝试 {self.connection_attempts}): {url}")
            
            # 🎯 错误处理：添加连接超时
            self.websocket = await asyncio.wait_for(
                websockets.connect(url), 
                timeout=10.0
            )
            self.is_connected = True
            self.connection_stable = False  # 需要等待握手成功
            
            # 启动接收任务
            self.recv_task = asyncio.create_task(self._receive_messages())
            
            logger.info("✅ 科大讯飞ASR连接成功")
            return True
            
        except asyncio.TimeoutError:
            logger.error("❌ 科大讯飞ASR连接超时")
            self._record_error("连接超时")
            return False
        except Exception as e:
            logger.error(f"❌ 科大讯飞ASR连接失败: {e}")
            self._record_error(str(e))
            self.is_connected = False
            return False
    
    def _record_error(self, error_msg: str) -> None:
        """记录错误并更新错误计数"""
        self.error_count += 1
        self.last_error_time = datetime.utcnow()
        logger.warning(f"⚠️ 科大讯飞错误记录: {error_msg} (累计错误: {self.error_count})")
        
        if self.error_count >= self.max_errors_before_disable:
            logger.error(f"❌ 科大讯飞错误次数达到上限({self.max_errors_before_disable})，将暂时禁用")
    
    async def start_transcription(self) -> bool:
        """开始转录会话"""
        try:
            if not self.is_connected:
                return False
            
            # 启动音频发送任务
            self.send_task = asyncio.create_task(self._send_audio_chunks())
            self.is_session_active = True
            
            logger.info("🎤 科大讯飞实时转录已启动")
            return True
            
        except Exception as e:
            logger.error(f"启动科大讯飞转录失败: {e}")
            return False
    
    async def send_audio_chunk(self, audio_data: bytes) -> None:
        """
        发送音频数据块到缓冲区 - 性能优化版
        支持批量发送和非阻塞队列
        """
        if not self.is_session_active:
            return
            
        try:
            # 🎯 性能优化：非阻塞放入队列
            if self.audio_buffer.full():
                # 队列满时丢弃最老的数据
                try:
                    self.audio_buffer.get_nowait()
                except asyncio.QueueEmpty:
                    pass
            
            await self.audio_buffer.put(audio_data)
            
        except Exception as e:
            logger.error(f"音频数据放入队列失败: {e}")
    
    async def _send_audio_chunks(self) -> None:
        """
        音频发送循环 - 性能优化版
        支持批量发送和自适应延迟
        """
        try:
            while self.is_session_active and self.is_connected:
                try:
                    # 🎯 性能优化：批量收集音频数据
                    collected_chunks = []
                    total_size = 0
                    
                    # 收集第一个块（阻塞等待）
                    first_chunk = await asyncio.wait_for(
                        self.audio_buffer.get(), 
                        timeout=1.0
                    )
                    collected_chunks.append(first_chunk)
                    total_size += len(first_chunk)
                    
                    # 🎯 性能优化：非阻塞收集更多块（如果有的话）
                    while total_size < self.batch_threshold:
                        try:
                            chunk = self.audio_buffer.get_nowait()
                            collected_chunks.append(chunk)
                            total_size += len(chunk)
                        except asyncio.QueueEmpty:
                            break
                    
                    # 🎯 性能优化：批量发送或逐个发送
                    if len(collected_chunks) == 1:
                        # 单个块直接发送
                        await self.websocket.send(collected_chunks[0])
                    else:
                        # 多个块可以选择合并发送或逐个发送
                        # 科大讯飞要求固定大小块，所以逐个发送
                        for chunk in collected_chunks:
                            await self.websocket.send(chunk)
                            # 减少批量发送时的延迟
                            await asyncio.sleep(self.chunk_interval / len(collected_chunks))
                    
                    # 🎯 性能优化：自适应延迟
                    if len(collected_chunks) > 1:
                        # 批量发送时减少额外延迟
                        await asyncio.sleep(self.chunk_interval * 0.5)
                    else:
                        # 单个发送时正常延迟
                        await asyncio.sleep(self.chunk_interval)
                    
                except asyncio.TimeoutError:
                    continue
                    
        except Exception as e:
            logger.error(f"科大讯飞音频发送错误: {e}")
    
    async def _receive_messages(self) -> None:
        """接收科大讯飞转录结果"""
        try:
            while self.is_connected:
                try:
                    message = await self.websocket.recv()
                    await self._handle_message(message)
                    
                except websockets.exceptions.ConnectionClosed:
                    logger.warning("科大讯飞WebSocket连接已关闭")
                    break
                    
        except Exception as e:
            logger.error(f"科大讯飞消息接收错误: {e}")
    
    async def _handle_message(self, message: str) -> None:
        """处理科大讯飞返回的消息 - 增强版错误处理"""
        try:
            data = json.loads(message)
            action = data.get("action")
            
            if action == "started":
                logger.info("🤝 科大讯飞握手成功")
                self.connection_stable = True  # 标记连接稳定
                # 🎯 错误处理：重置错误计数（连接成功）
                self.error_count = max(0, self.error_count - 1)
                
            elif action == "result":
                # 这是转录结果
                transcript = data.get("data", "")
                if transcript:
                    await self._publish_transcript_delta(transcript)
                    
            elif action == "error":
                # 🎯 错误处理：详细的错误分析和处理
                error_code = data.get("code", "unknown")
                error_desc = data.get("desc", "unknown error")
                logger.error(f"❌ 科大讯飞API错误: {error_code} - {error_desc}")
                
                # 根据错误类型决定是否需要重连或禁用
                if error_code in ["10106", "10105"]:  # 认证相关错误
                    logger.error("❌ 科大讯飞认证错误，请检查凭证配置")
                    self._record_error(f"认证错误: {error_desc}")
                elif error_code in ["10107", "10109"]:  # 配额或频率限制
                    logger.warning("⚠️ 科大讯飞配额限制，暂时降级")
                    self._record_error(f"配额限制: {error_desc}")
                else:
                    self._record_error(f"API错误: {error_code} - {error_desc}")
                
                # 标记连接不稳定
                self.connection_stable = False
                
        except json.JSONDecodeError as e:
            logger.error(f"❌ 科大讯飞消息JSON解析失败: {e}")
            self._record_error("JSON解析失败")
        except Exception as e:
            logger.error(f"❌ 处理科大讯飞消息失败: {e}")
            self._record_error(f"消息处理失败: {str(e)}")
    
    async def _publish_transcript_delta(self, transcript: str) -> None:
        """发布转录增量到消息总线"""
        try:
            message_data = create_message(
                event_type=EventType.TRANSCRIPT_DELTA,
                message_type=MessageType.LISTENER_TRANSCRIPT,
                data={
                    "delta": transcript,
                    "text": transcript,
                    "session_id": self.current_session_id,
                    "source": "xunfei",
                    "is_delta": True,
                    "is_final": False,
                    "timestamp": datetime.utcnow().isoformat(),
                    "confidence": 0.8  # 科大讯飞通常有较高置信度
                },
                source_agent="xunfei_asr_manager"
            )
            
            await message_bus.publish(TopicNames.TRANSCRIPT_DELTA, message_data)
            logger.info(f"📡 科大讯飞转录增量已发布: '{transcript}'")
            
        except Exception as e:
            logger.error(f"发布科大讯飞转录失败: {e}")
    
    async def stop_transcription(self) -> None:
        """停止转录会话"""
        try:
            self.is_session_active = False
            
            # 发送结束标记
            if self.websocket and self.is_connected:
                end_tag = {"end": True}
                await self.websocket.send(json.dumps(end_tag).encode('utf-8'))
            
            # 取消任务
            if self.send_task:
                self.send_task.cancel()
            
            logger.info("🛑 科大讯飞转录已停止")
            
        except Exception as e:
            logger.error(f"停止科大讯飞转录失败: {e}")
    
    async def disconnect(self) -> None:
        """断开连接"""
        try:
            await self.stop_transcription()
            
            if self.recv_task:
                self.recv_task.cancel()
            
            if self.websocket:
                await self.websocket.close()
            
            self.is_connected = False
            logger.info("🔌 科大讯飞ASR连接已断开")
            
        except Exception as e:
            logger.error(f"断开科大讯飞连接失败: {e}") 