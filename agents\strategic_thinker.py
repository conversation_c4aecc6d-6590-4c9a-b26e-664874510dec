# Fully refactored agents/strategic_thinker.py

import asyncio
import json
from typing import Dict, Any, List
from loguru import logger
from datetime import datetime
from thefuzz import fuzz

from core.message_bus import message_bus, TopicNames, create_message
from core.event_types import EventType, MessageType
from core.config import settings
from core.base_agent import BaseAgent
from core.client_factory import ClientFactory

class StrategicThinker(BaseAgent):
    """
    Strategic Thinker for deep, context-aware analysis to manage conversation flow.
    Focuses on identifying disruptive interruption opportunities like verbosity and repetition.
    """
    
    def __init__(self):
        super().__init__("strategic_thinker")
        self.agent_id = "strategic_thinker"  # 添加agent_id属性
        self.client = None
        self.conversation_context: Dict[str, List[Dict[str, Any]]] = {}
        self.delta_buffers: Dict[str, str] = {}
        self.analysis_tasks: Dict[str, asyncio.Task] = {}
        self.buffer_min_length = settings.strategic_buffer_min_length
        self.buffer_max_length = settings.strategic_buffer_max_length
        self.disruptive_threshold = settings.disruptive_interruption_threshold
        
    async def initialize(self) -> bool:
        """Initializes client and subscribes to all necessary transcript topics."""
        try:
            self.client = await ClientFactory.get_doubao_client()
            await message_bus.connect()
            
            await message_bus.subscribe(TopicNames.TRANSCRIPT_DELTA, self.handle_transcript_delta)
            await message_bus.subscribe(TopicNames.TRANSCRIPT_COMPLETED, self.handle_transcript_completed)
            await message_bus.subscribe(TopicNames.AI_TRANSCRIPT_EVENTS, self.handle_ai_transcript)
            
            self.logger.info("Strategic Thinker initialized with full context awareness.")
            return True
        except Exception as e:
            self.logger.error(f"Strategic Thinker initialization failed: {e}")
            return False

    async def start(self) -> bool:
        """启动Strategic Thinker agent."""
        try:
            self.logger.info("Starting Strategic Thinker agent...")
            # Strategic Thinker是事件驱动的，不需要额外的启动逻辑
            # 所有必要的订阅已在initialize()中完成
            
            # 设置运行状态标志
            self.is_running = True
            
            self.logger.info("Strategic Thinker agent started successfully")
            return True
        except Exception as e:
            self.logger.error(f"Failed to start Strategic Thinker: {e}")
            self.is_running = False
            return False

    async def shutdown(self) -> None:
        """关闭Strategic Thinker agent并清理资源."""
        try:
            self.logger.info("Shutting down Strategic Thinker agent...")
            
            # 取消所有正在进行的分析任务
            for session_id, task in list(self.analysis_tasks.items()):
                if not task.done():
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass
            self.analysis_tasks.clear()
            
            # 清理会话上下文和缓冲区
            self.conversation_context.clear()
            self.delta_buffers.clear()
            
            # 关闭AI客户端连接
            if self.client:
                # ClientFactory会处理连接的清理
                self.client = None
            
            self.logger.info("Strategic Thinker shutdown complete")
            
        except Exception as e:
            self.logger.error(f"Error during Strategic Thinker shutdown: {e}")

    async def _append_to_session_context(self, session_id: str, role: str, content: str):
        """Appends a message to context and manages memory with a sliding window."""
        if session_id not in self.conversation_context:
            self.conversation_context[session_id] = []
        
        self.conversation_context[session_id].append({"role": role, "content": content})
        
        if len(self.conversation_context[session_id]) > 20:
            self.conversation_context[session_id] = self.conversation_context[session_id][-20:]

    async def handle_ai_transcript(self, topic: str, message: Dict[str, Any]):
        data = message.get("data", {})
        session_id = data.get("session_id")
        if session_id and (data.get("is_final") or data.get("complete")):
            transcript = data.get("transcript", "")
            if transcript:
                await self._append_to_session_context(session_id, "assistant", transcript)

    async def handle_transcript_completed(self, topic: str, message: Dict[str, Any]):
        data = message.get("data", {})
        session_id = data.get("session_id")
        transcript = data.get("transcript", "")
        
        # 🎯 新增调试日志
        self.logger.info(f"🧠 StrategicThinker received COMPLETED for session {session_id}: '{transcript}'")
        
        if session_id and transcript:
            await self._append_to_session_context(session_id, "user", transcript)
            await self._trigger_analysis(transcript, session_id)
            if session_id in self.delta_buffers:
                self.delta_buffers[session_id] = ""

    async def handle_transcript_delta(self, topic: str, message: Dict[str, Any]):
        data = message.get("data", {})
        session_id = data.get("session_id")
        delta = data.get("delta", "")
        source = data.get("source", "qwen")  # 默认为千问源
        
        if not session_id or not delta:
            return

        # 🎯 源识别调试日志
        self.logger.info(f"🧠 StrategicThinker received DELTA from {source} for session {session_id}: '{delta}'")

        # 🎯 优先处理科大讯飞的实时数据（更适合实时深度分析）
        if source == "xunfei":
            buffer = self.delta_buffers.get(session_id, "") + delta
            self.delta_buffers[session_id] = buffer
            await self._trigger_analysis(buffer, session_id, source="xunfei")
        else:
            # 千问数据作为补充和验证
            self.logger.debug(f"📝 STRATEGIC: Qwen delta received as backup: '{delta}'")
            # 仅当科大讯飞不可用时使用千问数据
            if session_id not in self.delta_buffers:
                buffer = self.delta_buffers.get(session_id, "") + delta
                self.delta_buffers[session_id] = buffer
                await self._trigger_analysis(buffer, session_id, source="qwen")

    async def _trigger_analysis(self, text: str, session_id: str, source: str = "qwen"):
        if session_id in self.analysis_tasks and not self.analysis_tasks[session_id].done():
            return
        
        text_len = len(text)
        # 🎯 科大讯飞数据使用稍低的触发阈值（平衡实时性和准确性）
        min_threshold = self.buffer_min_length if source == "qwen" else max(10, self.buffer_min_length // 1.5)
        
        if text_len >= min_threshold:
            is_over_max = text_len >= self.buffer_max_length
            self.logger.info(f"🚀 STRATEGIC: Triggering analysis for {source} source (len={text_len}, threshold={min_threshold})")
            self.analysis_tasks[session_id] = asyncio.create_task(
                self._perform_disruptive_analysis(text, session_id, is_over_max, source)
            )

    def _check_repetition(self, session_id: str, current_buffer: str) -> float:
        """检查当前发言与历史发言的相似度"""
        if session_id not in self.conversation_context:
            return 0.0
            
        history = self.conversation_context[session_id]
        if not history:
            return 0.0
            
        # 只比较最近的用户发言
        user_history = [turn['content'] for turn in history if turn['role'] == 'user']
        if not user_history:
            return 0.0
            
        # 与最近的2-3轮对话比较
        max_similarity = 0
        for past_utterance in user_history[-3:]:
            similarity = fuzz.ratio(current_buffer, past_utterance)
            if similarity > max_similarity:
                max_similarity = similarity
                
        return max_similarity

    def _get_disruptive_analysis_prompt(self) -> str:
        return """
        You are a conversation flow manager. Your goal is to identify moments where a polite interruption would improve conversation quality.
        
        Analyze the user's latest utterance in the context of the last 3-4 turns of conversation.
        
        Focus on these specific patterns:
        1. **Semantic Repetition:** Is the user asking the *same question* or making the *same point* they've already made recently, even if using different words?
           - **CRITICAL RULE**: Before flagging as repetition, you MUST check if the AI's previous answer actually addressed the user's question. If the AI evaded the question, gave a wrong answer, or changed the topic, the user re-asking is a **VALID CLARIFICATION**, not a repetition. In this case, confidence must be 0.0.
        2. **Verbosity/Rambling:** Is the user going in circles or providing excessive detail without making progress? (Confidence > 0.7)
        3. **Off-topic:** Has the user completely changed subjects without resolution? (Confidence > 0.6)
        
        **Example of CORRECT repetition detection:**
        User: "Tell me about famous companies in Hangzhou."
        AI: "In Hangzhou, famous companies include Alibaba, NetEase, and Wahaha."
        User: "Okay, what about famous companies in Hangzhou?" --> THIS IS REPETITION. 

        **Example of INCORRECT repetition detection (AI Fault):**
        User: "Tell me about famous companies in Hangzhou."
        AI: "Hangzhou is a beautiful city with the West Lake..."
        User: "No, I asked about famous COMPANIES in Hangzhou." --> THIS IS A VALID CLARIFICATION. Confidence: 0.0.
        User: "杭州这边有没有什么著名的企业？" --> User is re-asking because AI didn't answer properly. THIS IS NOT REPETITION. Confidence: 0.0.
        
        The `suggested_response` must be conversational and helpful, naturally reflecting the interruption reason.
        
        Respond ONLY with a valid JSON object:
        {
          "confidence": <float, 0.0 to 1.0>,
          "reason": "<string, specific reason>",
          "interruption_type": "<string, 'repetition', 'verbosity', 'off_topic', 'correction'>",
          "suggested_response": "<string, a complete, polite, ready-to-speak sentence>"
        }
        """

    async def _perform_disruptive_analysis(self, text: str, session_id: str, force_reset: bool, source: str = "qwen"):
        """Performs LLM analysis and publishes to DISRUPTIVE_ANALYSIS topic if confidence is high."""
        try:
            # 🎯 禁用简单的文本相似度检查，让LLM自己进行语义分析
            # repetition_score = self._check_repetition(session_id, text)  # <-- 注释掉这一行
            # repetition_info = ""
            # if repetition_score > 80:  # 相似度阈值，可以调整
            #     self.logger.info(f"🔥 High repetition detected for session {session_id}: {repetition_score}%")
            #     repetition_info = f"\n\nREPETITION DETECTED: Current speech is {repetition_score}% similar to recent utterances."
            
            context = self.conversation_context.get(session_id, [])
            context_summary = "\n".join([f"{msg['role']}: {msg['content']}" for msg in context])
            # 🎯 移除repetition_info的添加
            prompt_content = f"Conversation Context:\n{context_summary}\n\nUser's real-time speech:\n{text}"

            response = await self.client.chat.completions.create(
                model=settings.strategic_thinker_model,
                messages=[
                    {"role": "system", "content": self._get_disruptive_analysis_prompt()},
                    {"role": "user", "content": prompt_content}
                ],
                temperature=0.6, max_tokens=200
            )

            analysis = json.loads(response.choices[0].message.content)
            confidence = analysis.get("confidence", 0.0)
            reason = analysis.get("reason", "")
            
            # 🎯 新增调试日志
            self.logger.info(f"🧠 Disruptive analysis result for {session_id}: confidence={confidence:.2f}, reason='{reason}'")

            if confidence > self.disruptive_threshold:
                await message_bus.publish(TopicNames.DISRUPTIVE_ANALYSIS, create_message(
                    event_type=EventType.STRATEGIC_ANALYSIS,
                    message_type=MessageType.STRATEGIC_CONTENT,
                    data={"session_id": session_id, "analysis": analysis},
                    source_agent=self.agent_id
                ))
                self.delta_buffers[session_id] = "" # Reset buffer
        except Exception as e:
            # 🎯 修复日志语法 - 使用标准logging的exception方法
            self.logger.exception(
                f"Disruptive analysis failed for session {session_id}: {e}"
            )
        finally:
            if force_reset and session_id in self.delta_buffers:
                self.delta_buffers[session_id] = ""
            if session_id in self.analysis_tasks:
                del self.analysis_tasks[session_id] 