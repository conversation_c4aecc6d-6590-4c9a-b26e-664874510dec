# Audio Agent 环境变量配置示例
# 复制此文件为 .env 并填入您的实际配置值
#
# VAD重构说明:
# - 简化VAD逻辑为两个模式: STANDARD (标准模式) 和 BARGE_IN (打断模式)
# - STANDARD模式用于正常对话，包括interview场景
# - BARGE_IN模式用于AI说话时用户插话需要快速停止音频
# - 系统会根据对话状态自动切换VAD模式

# 阿里千问 配置 (用于Realtime API - Listener和Speaker)
QWEN_API_KEY=your_qwen_api_key_here
QWEN_REALTIME_MODEL=qwen-omni-turbo-realtime
QWEN_BASE_URL=https://dashscope.aliyuncs.com/api-ws/v1/
QWEN_WS_ENDPOINT=wss://dashscope.aliyuncs.com/api-ws/v1/realtime

# 字节跳动豆包 配置 (用于Tactical Thinker、Strategic Thinker和Decider)
DOUBAO_API_KEY=your_doubao_api_key_here
DOUBAO_BASE_URL=https://ark.cn-beijing.volces.com/api/v3

# 音频配置
AUDIO_SAMPLE_RATE=24000
AUDIO_CHANNELS=1
AUDIO_FORMAT=pcm16
AUDIO_CHUNK_SIZE=1024

# 系统配置
LOG_LEVEL=INFO
MAX_CONVERSATIONS=10
INTERRUPTION_THRESHOLD=0.5

# WebSocket配置
WS_HOST=0.0.0.0
WS_PORT=8000

# AI Agent配置 - 豆包模型
STRATEGIC_THINKER_MODEL=doubao-1-5-lite-32k-250115
TACTICAL_THINKER_MODEL=doubao-1-5-lite-32k-250115
DECIDER_MODEL=doubao-1-5-lite-32k-250115

# 打断逻辑配置 (优化后的设置) * AI打断人逻辑（还需要优化）*
COOPERATIVE_INTERRUPTION_THRESHOLD=0.4
DISRUPTIVE_INTERRUPTION_THRESHOLD=0.3
SILENCE_DURATION_MS=100
RESPONSE_DELAY_MS=100

# VAD (语音活动检测) 配置 - 简化为两个模式
# STANDARD模式: 标准对话模式 (包括interview场景)
VAD_STANDARD_THRESHOLD=0.5
VAD_STANDARD_PREFIX_PADDING_MS=500
VAD_STANDARD_SILENCE_DURATION_MS=900

# BARGE_IN模式: 打断模式 (AI说话时用户插话)
VAD_BARGE_IN_THRESHOLD=0.1
VAD_BARGE_IN_PREFIX_PADDING_MS=100
VAD_BARGE_IN_SILENCE_DURATION_MS=1000

# 科大讯飞实时语音转写配置
XUNFEI_APP_ID=your_xunfei_app_id_here
XUNFEI_API_KEY=your_xunfei_api_key_here
XUNFEI_ASR_URL=wss://rtasr.xfyun.cn/v1/ws
XUNFEI_AUDIO_FORMAT=pcm16
XUNFEI_SAMPLE_RATE=16000
XUNFEI_CHUNK_SIZE=1280
XUNFEI_CHUNK_INTERVAL_MS=40

# 双轨ASR配置
ENABLE_DUAL_ASR=true
XUNFEI_CONFIDENCE_THRESHOLD=0.6

# AI面试官模式配置
# 注意: 面试官模式使用STANDARD VAD配置，不需要特殊的VAD设置
INTERVIEWER_MODE=false
INTERVIEWER_MAX_SPEAKING_TIME=15
INTERVIEWER_FORCE_INTERRUPT_AFTER=30
INTERVIEWER_COOPERATIVE_THRESHOLD=0.3
INTERVIEWER_DISRUPTIVE_THRESHOLD=0.2
INTERVIEWER_TOPIC_DRIFT_THRESHOLD=0.6
INTERVIEWER_VERBOSITY_THRESHOLD=0.7

# Decider配置
DECIDER_STRATEGIC_TIMEOUT=3

# 连接管理配置
AUTO_RECONNECT=true

# Audio Agent API Configuration Guide

# 1. 阿里千问qwen-omni-turbo-realtime API 需要阿里云平台的API密钥，支持实时语音交互
# 2. 现在 Listener 和 Speaker 使用阿里千问qwen-omni-turbo-realtime API的单一连接
# 3. QWEN_REALTIME_MODEL 使用 qwen-omni-turbo-realtime 模型
# 4. 字节跳动豆包 API 用于 Tactical Thinker、Strategic Thinker 和 Decider
# 5. 豆包模型使用 doubao-1-5-lite-32k-250115 (快速分析) 和 doubao-1-5-lite-32k-250115 (深度分析)
# 6. SILENCE_DURATION_MS 已优化为100ms以支持实时转录和AI打断
# 7. AUTO_RECONNECT=true 启用自动重连机制

# 如何获取API密钥：
# - 阿里千问: https://dashscope.aliyuncs.com/
# - 字节跳动豆包: https://console.volcengine.com/ark/ 