# Fully refactored agents/tactical_thinker.py

import asyncio
import json
from typing import Dict, Any, List
from loguru import logger
from datetime import datetime

from core.message_bus import message_bus, TopicNames, create_message
from core.event_types import EventType, MessageType
from core.config import settings
from core.base_agent import BaseAgent
from core.client_factory import ClientFactory

class TacticalThinker(BaseAgent):
    """
    Tactical Thinker for cooperative, non-spoken semantic analysis.
    It maintains conversation context to provide timely and natural text/emoji feedback.
    """
    
    def __init__(self):
        super().__init__("tactical_thinker")
        self.agent_id = "tactical_thinker"  # 添加agent_id属性
        self.client = None
        self.conversation_context: Dict[str, List[Dict[str, Any]]] = {}
        self.delta_buffers: Dict[str, str] = {}
        self.analysis_tasks: Dict[str, asyncio.Task] = {}
        self.buffer_min_length = settings.tactical_buffer_min_length
        self.buffer_max_length = settings.tactical_buffer_max_length
        # 🎯 调试步骤：暂时将阈值降到极低，观察是否有任何输出
        self.cooperative_threshold = 0.05  # settings.cooperative_interruption_threshold
        self.logger.warning(f"⚠️ TACTICAL THINKER: Cooperative threshold is temporarily set to {self.cooperative_threshold} for debugging!")
    
    async def initialize(self) -> bool:
        """Initializes client and subscribes to all necessary transcript topics."""
        try:
            self.client = await ClientFactory.get_doubao_client()
            await message_bus.connect()
            
            # Subscribe to user AND AI transcripts to build full conversation context
            await message_bus.subscribe(TopicNames.TRANSCRIPT_DELTA, self.handle_transcript_delta)
            await message_bus.subscribe(TopicNames.TRANSCRIPT_COMPLETED, self.handle_transcript_completed)
            await message_bus.subscribe(TopicNames.AI_TRANSCRIPT_EVENTS, self.handle_ai_transcript)
            
            self.logger.info("Tactical Thinker initialized with full context awareness.")
            return True
        except Exception as e:
            self.logger.error(f"Tactical Thinker initialization failed: {e}")
            return False

    async def start(self) -> bool:
        """启动Tactical Thinker agent."""
        try:
            self.logger.info("Starting Tactical Thinker agent...")
            # Tactical Thinker是事件驱动的，不需要额外的启动逻辑
            # 所有必要的订阅已在initialize()中完成
            
            # 设置运行状态标志
            self.is_running = True
            
            self.logger.info("Tactical Thinker agent started successfully")
            return True
        except Exception as e:
            self.logger.error(f"Failed to start Tactical Thinker: {e}")
            self.is_running = False
            return False

    async def shutdown(self) -> None:
        """关闭Tactical Thinker agent并清理资源."""
        try:
            self.logger.info("Shutting down Tactical Thinker agent...")
            
            # 取消所有正在进行的分析任务
            for session_id, task in list(self.analysis_tasks.items()):
                if not task.done():
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass
            self.analysis_tasks.clear()
            
            # 清理会话上下文和缓冲区
            self.conversation_context.clear()
            self.delta_buffers.clear()
            
            # 关闭AI客户端连接
            if self.client:
                # ClientFactory会处理连接的清理
                self.client = None
            
            self.logger.info("Tactical Thinker shutdown complete")
            
        except Exception as e:
            self.logger.error(f"Error during Tactical Thinker shutdown: {e}")

    async def _append_to_session_context(self, session_id: str, role: str, content: str):
        """Appends a message to context and manages memory with a sliding window."""
        if session_id not in self.conversation_context:
            self.conversation_context[session_id] = []
        
        self.conversation_context[session_id].append({"role": role, "content": content})
        
        # This implements the sliding window to keep the last 20 turns
        if len(self.conversation_context[session_id]) > 20:
            self.conversation_context[session_id] = self.conversation_context[session_id][-20:]

    async def handle_ai_transcript(self, topic: str, message: Dict[str, Any]):
        """Adds AI responses to the conversation context."""
        data = message.get("data", {})
        session_id = data.get("session_id")
        if session_id and (data.get("is_final") or data.get("complete")):
            transcript = data.get("transcript", "")
            if transcript:
                await self._append_to_session_context(session_id, "assistant", transcript)

    async def handle_transcript_completed(self, topic: str, message: Dict[str, Any]):
        """Handles final user transcripts to update context and run a final analysis."""
        data = message.get("data", {})
        session_id = data.get("session_id")
        transcript = data.get("transcript", "")
        
        # 🎯 新增调试日志
        self.logger.info(f"🤝 TacticalThinker received COMPLETED for session {session_id}: '{transcript}'")
        
        if session_id and transcript:
            await self._append_to_session_context(session_id, "user", transcript)
            # Run a final analysis on the complete sentence
            await self._trigger_analysis(transcript, session_id)
            # Clear the delta buffer for the session
            if session_id in self.delta_buffers:
                self.delta_buffers[session_id] = ""

    async def handle_transcript_delta(self, topic: str, message: Dict[str, Any]):
        """Buffers deltas and triggers analysis - 增强版支持多源转录."""
        data = message.get("data", {})
        session_id = data.get("session_id")
        delta = data.get("delta", "")
        source = data.get("source", "qwen")  # 默认为千问源
        
        if not session_id or not delta:
            return

        # 🎯 源识别调试日志
        self.logger.info(f"🤝 TacticalThinker received DELTA from {source} for session {session_id}: '{delta}'")

        # 🎯 优先处理科大讯飞的实时数据（更适合快速分析）
        if source == "xunfei":
            buffer = self.delta_buffers.get(session_id, "") + delta
            self.delta_buffers[session_id] = buffer

            # 调试日志
            self.logger.info(f"🧠 TACTICAL: XunFei buffer for {session_id} is now {len(buffer)} chars: '{buffer}'")
            
            # 科大讯飞数据触发更频繁的分析（降低阈值）
            await self._trigger_analysis(buffer, session_id, source="xunfei")
        else:
            # 千问数据作为备选和验证
            self.logger.debug(f"📝 TACTICAL: Qwen delta received as backup: '{delta}'")
            # 仅当科大讯飞不可用时使用千问数据
            if session_id not in self.delta_buffers:
                buffer = self.delta_buffers.get(session_id, "") + delta
                self.delta_buffers[session_id] = buffer
                await self._trigger_analysis(buffer, session_id, source="qwen")

    async def _trigger_analysis(self, text: str, session_id: str, source: str = "qwen"):
        """Checks conditions and starts an analysis task if appropriate."""
        if session_id in self.analysis_tasks and not self.analysis_tasks[session_id].done():
            return

        text_len = len(text)
        # 🎯 科大讯飞数据使用更低的触发阈值（更敏感）
        min_threshold = self.buffer_min_length if source == "qwen" else max(5, self.buffer_min_length // 2)
        
        if text_len >= min_threshold:
            is_over_max = text_len >= self.buffer_max_length
            self.logger.info(f"🚀 TACTICAL: Triggering analysis for {source} source (len={text_len}, threshold={min_threshold})")
            self.analysis_tasks[session_id] = asyncio.create_task(
                self._perform_cooperative_analysis(text, session_id, is_over_max, source)
            )

    def _get_cooperative_analysis_prompt(self) -> str:
        return """
        You are an extremely empathetic and supportive conversation partner. Your ONLY job is to provide very short, encouraging, text-based interjections (like '【嗯嗯】' or '【我懂了👍】') while the user is talking.
        
        **Your default action should be to find a reason to agree or support.** Be proactive. Only stay silent if an interjection would be truly awkward (like during a direct question).
        
        Analyze the user's INCOMPLETE, REAL-TIME speech.
        
        Respond ONLY with a valid JSON object:
        {
          "confidence": <float, 0.0 to 1.0>,
          "interjection": "<string>",
          "reason": "<string>"
        }
        
        **Interjection Rules:**
        1. MUST be very short (2-5 characters).
        2. MUST be enclosed in brackets `【】`.
        3. Examples: 【嗯嗯】, 【是的】, 【我理解】, 【没错】, 【太好了🎉】, 【原来如此】, 【确实】, 【我懂了👍】
        
        **Decision Logic:**
        - If the user expresses any opinion, feeling, or statement of fact, your confidence should be HIGH (>0.7).
        - If the user seems to be trailing off or has paused, your confidence should be HIGH (>0.8).
        - If the user asks a direct question to the AI, confidence MUST be 0.0.
        - If the user says something like "杭州这边有没有什么著名的企业", confidence should be >0.6 with interjection like 【嗯嗯】
        
        Your analysis must be extremely fast. Be bold and supportive!
        """

    async def _perform_cooperative_analysis(self, text: str, session_id: str, force_reset: bool, source: str = "qwen"):
        """Performs LLM analysis and publishes to UI_UPDATE topic if confidence is high."""
        try:
            context = self.conversation_context.get(session_id, [])
            context_summary = "\n".join([f"{msg['role']}: {msg['content']}" for msg in context])
            prompt_content = f"Conversation Context:\n{context_summary}\n\nUser's real-time speech:\n{text}"

            response = await self.client.chat.completions.create(
                model=settings.tactical_thinker_model,
                messages=[
                    {"role": "system", "content": self._get_cooperative_analysis_prompt()},
                    {"role": "user", "content": prompt_content}
                ],
                temperature=0.1, max_tokens=150
            )
            
            raw_response_content = response.choices[0].message.content
            # 🎯 调试日志：打印从AI模型收到的原始JSON字符串
            self.logger.info(f"🧠 TACTICAL: Raw LLM response for {session_id}: {raw_response_content}")
            
            analysis = json.loads(raw_response_content)
            confidence = analysis.get("confidence", 0.0)
            interjection = analysis.get("interjection", "")
            reason = analysis.get("reason", "No reason provided.")  # 获取原因
            
            # 🎯 新增调试日志，包含原因
            self.logger.info(f"🤝 Analysis result for {session_id}: confidence={confidence:.2f}, interjection='{interjection}', reason='{reason}'")

            if confidence > self.cooperative_threshold and interjection:  # 仅在有内容时发送
                await message_bus.publish(TopicNames.UI_UPDATE, create_message(
                    event_type=EventType.UI_UPDATE,
                    message_type=MessageType.TACTICAL_INTENT,
                    data={"session_id": session_id, "type": "cooperative_interjection", "text": interjection},
                    source_agent=self.agent_id
                ))
                self.logger.info(f"📤 Sent interjection to UI: '{interjection}' (confidence: {confidence:.2f})")
                self.delta_buffers[session_id] = ""  # Reset buffer after successful interjection
            else:
                if confidence <= self.cooperative_threshold:
                    self.logger.debug(f"🤐 Confidence too low ({confidence:.2f} <= {self.cooperative_threshold}): {reason}")
                if not interjection:
                    self.logger.debug(f"🤐 Empty interjection, not sending to UI: {reason}")
        except json.JSONDecodeError:
            self.logger.error(f"Failed to decode LLM JSON response for {session_id}: {raw_response_content}")
        except Exception as e:
            # 🎯 修复日志语法 - 使用标准logging的exception方法
            self.logger.exception(
                f"Cooperative analysis failed for session {session_id}: {e}"
            )
        finally:
            if force_reset and session_id in self.delta_buffers:
                self.delta_buffers[session_id] = ""
            if session_id in self.analysis_tasks:
                del self.analysis_tasks[session_id] 